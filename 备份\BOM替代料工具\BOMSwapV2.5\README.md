# BOM替代料工具 V2.2

## 软件简介
BOM替代料工具是一款专业的电子制造业辅助软件，专为解决电子BOM（物料清单）管理过程中的替代料处理而设计。本工具能自动识别并处理BOM表中的替代料关系，显著提高物料管理效率和准确性。

## 主要功能
1. **智能替代料处理**
   - 自动识别BOM中的替代料关系
   - 支持批量添加替代料
   - 智能合并相同料号
   - 自动计算位号数量
   - 优化替代料显示逻辑

2. **BOM预处理**
   - 自动识别和保留项目信息行
   - 保持原始格式（字体、颜色、边框、对齐等）
   - 优化Item编号逻辑
   - 支持复杂编号处理
   - 改进工作表复制功能

3. **表头配置**
   - 支持自定义表头关键字
   - 保存表头配置方便下次使用
   - 智能表头匹配算法
   - 适应不同格式的BOM表
   - 新增表头配置重置功能

4. **数据统计与分析**
   - 替代料统计
   - 合并物料详情
   - 处理时长统计
   - 位号数量统计
   - 优化统计信息显示

5. **用户界面**
   - macOS风格现代界面
   - 优化的文件选择区域
   - 实时状态信息显示
   - 流畅的视觉交互体验
   - 简化的设置入口

## 使用要求
- 操作系统：Windows 10/11
- Python 3.6+
- 依赖包：
  - pandas
  - openpyxl
  - tkinter

## 安装说明
1. 确保已安装Python 3.6或更高版本
2. 安装所需依赖包：
   ```bash
   pip install pandas openpyxl
   ```
3. 运行程序：
   ```bash
   python bom_tool.py
   ```

## 使用说明
1. **选择文件**
   - 点击"浏览..."按钮选择原始BOM文件
   - 点击"浏览..."按钮选择替代料表文件
   - 可以设置默认替代料表路径

2. **配置表头**
   - 点击"设置"按钮打开配置对话框
   - 为各字段设置对应的表头名称
   - 点击"保存配置"按钮保存设置
   - 点击"恢复默认"可重置为默认表头
   - 使用"重置所有配置"可完全重置

3. **开始处理**
   - 点击"开始处理"按钮开始处理
   - 程序会自动处理并生成新的BOM文件
   - 处理过程中可在状态区域查看实时进度
   - 处理完成后会显示详细的统计信息

4. **输出结果**
   - 新BOM文件将保存在原文件同目录下
   - 文件名格式：原文件名_替代料.xlsx
   - 保留原始项目信息行格式
   - 替代料行使用可配置的高亮颜色标记
   - 保留原始BOM的其他工作表

## 界面布局说明
新版UI采用macOS风格设计，布局优化为以下几个主要区域:

1. **标题区域** - 显示工具名称和版本信息
   - 左侧显示软件名称和版本号
   - 右上角设置了"设置"和"帮助"按钮，提高操作便捷性

2. **文件选择区域** - 提供BOM文件和替代料表的选择功能
   - 简洁的文件路径显示
   - 优化的浏览按钮布局
   - 智能的默认路径记忆功能

3. **操作按钮区域** - 包含核心功能按钮
   - "开始处理"按钮使用主题色突出显示
   - 简化的按钮布局，突出主要功能

4. **处理进度区域** - 显示处理状态和进度信息
   - 现代化的进度条设计
   - 清晰的百分比显示
   - 格式化的处理信息展示
   - 多样化的状态图标标识

5. **底部信息栏** - 显示开发者信息和版本日期
   - 简洁的布局设计
   - 清晰的联系信息

## 表头配置说明
以下是需要配置的表头字段及其说明：

| 字段标识 | 默认表头名称 | 说明 |
|---------|------------|------|
| item | Item | 物料的序号字段 |
| pn | PN | 物料的唯一标识编号 |
| part | Part | 物料的零件名称 |
| reference | Reference | 物料在PCB上的位置标识 |
| quantity | Quantity | 物料的数量 |
| description | Description | 物料的描述信息 |
| mfr_pn | ManufacturerPN | 物料的制造商料号 |
| manufacturer | Manufacturer | 物料的制造商名称 |
| attribute | attribute | 替代料表中的属性字段，用于识别替代料组 |

## 注意事项
1. BOM文件必须包含各主要字段的对应列，可通过表头配置适配不同格式
2. 替代料表必须包含与BOM相同的物料编号字段和属性字段
3. 同一替代组内的物料必须具有相同的attribute值
4. 处理大文件时可能需要较长时间，请耐心等待
5. 可以通过设置对话框自定义替代料的高亮颜色

## 技术支持
- 开发者：小航
- 联系方式：微信 XiaoHang_Sky

## 更新历史

### V2.3 (2025.3.19)
- 全面升级用户界面，采用macOS风格设计
  - 优化整体布局和视觉效果
  - 简化操作按钮，突出主要功能
  - 改进文件选择区域的交互体验
  - 优化进度显示和状态信息展示
- 增强功能改进
  - 新增替代料颜色自定义功能
  - 优化表头配置界面
  - 改进配置管理功能
  - 完善工作表复制功能
- 性能优化
  - 改进DataFrame处理逻辑
  - 优化文件读写性能
  - 提升大文件处理效率
- 界面交互优化
  - 简化设置入口
  - 优化帮助文档展示
  - 改进错误提示方式
  - 增强用户操作反馈

### V2.1 (2025.3.14)
- 修复item号误判问题
- 修复项目信息行格式问题
- 优化Excel文件处理
- 改进替代料编号逻辑
- 优化编号逻辑
- 提升处理稳定性

### V2.0 (2025.3.8)
- 增强核心功能
- 优化界面交互
- 完善统计信息

### V1.0 (2025.3.5)
- 首次发布
- 基础替代料处理功能 