# UTF-8编码
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2, 5, 0, 0),  # 文件版本（必须四段数字）
    prodvers=(2, 5, 0, 0),   # 产品版本
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'FileDescription', u'BOM替代料工具'),
            StringStruct(u'FileVersion', u'*******'),  # 必须与filevers一致
            StringStruct(u'ProductVersion', u'*******'), # 必须与prodvers一致
            StringStruct(u'LegalCopyright', u'版权所有 (C) 2025'),
            StringStruct(u'ProductName', u'BOM替代料工具'),
            StringStruct(u'OriginalFilename', u'BOM替代料工具.exe')
          ])
      ]),
    VarFileInfo([VarStruct(u'Translation', [0x409, 1200])])
  ]
)